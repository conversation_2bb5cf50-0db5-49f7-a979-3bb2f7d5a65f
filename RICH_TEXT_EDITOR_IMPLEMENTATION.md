# 富文本编辑器技术实现方案

## 编辑器选型：Tiptap

### 安装依赖

```bash
pnpm install @tiptap/react @tiptap/pm @tiptap/starter-kit
pnpm install @tiptap/extension-image @tiptap/extension-link @tiptap/extension-color
pnpm install @tiptap/extension-text-style @tiptap/extension-list-item @tiptap/extension-bullet-list
pnpm install @tiptap/extension-ordered-list @tiptap/extension-text-align
```

## 前端实现

### 1. 富文本编辑器组件

```typescript
// components/RichTextEditor.tsx
import React, { useCallback } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import { Button } from '@/components/ui/button'
import {
  Bold, Italic, Underline, Strikethrough,
  Heading1, Heading2, Heading3,
  List, ListOrdered, AlignLeft, AlignCenter, AlignRight,
  Link as LinkIcon, Image as ImageIcon, Palette
} from 'lucide-react'

interface RichTextEditorProps {
  content: string
  onChange: (content: string) => void
  placeholder?: string
  className?: string
}

export default function RichTextEditor({
  content,
  onChange,
  placeholder = '请输入内容...',
  className = ''
}: RichTextEditorProps) {

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 underline cursor-pointer',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      TextStyle,
      Color,
    ],
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML())
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4',
      },
    },
  })

  // 图片上传处理
  const handleImageUpload = useCallback(async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        const formData = new FormData()
        formData.append('image', file)

        const response = await fetch('/api/upload/images', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) throw new Error('上传失败')

        const { url } = await response.json()
        editor?.chain().focus().setImage({ src: url }).run()
      } catch (error) {
        console.error('图片上传失败:', error)
        alert('图片上传失败，请重试')
      }
    }
    input.click()
  }, [editor])

  // 添加链接
  const handleAddLink = useCallback(() => {
    const url = window.prompt('请输入链接地址:')
    if (url) {
      editor?.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
    }
  }, [editor])

  if (!editor) {
    return <div>编辑器加载中...</div>
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      {/* 工具栏 */}
      <div className="border-b p-2 flex flex-wrap gap-1">
        {/* 标题 */}
        <Button
          variant={editor.isActive('heading', { level: 1 }) ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
        >
          <Heading1 className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive('heading', { level: 2 }) ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
        >
          <Heading2 className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive('heading', { level: 3 }) ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
        >
          <Heading3 className="h-4 w-4" />
        </Button>

        <div className="w-px h-6 bg-gray-300 mx-1" />

        {/* 文本样式 */}
        <Button
          variant={editor.isActive('bold') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive('italic') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive('strike') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().toggleStrike().run()}
        >
          <Strikethrough className="h-4 w-4" />
        </Button>

        <div className="w-px h-6 bg-gray-300 mx-1" />

        {/* 列表 */}
        <Button
          variant={editor.isActive('bulletList') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
        >
          <List className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive('orderedList') ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
        >
          <ListOrdered className="h-4 w-4" />
        </Button>

        <div className="w-px h-6 bg-gray-300 mx-1" />

        {/* 对齐 */}
        <Button
          variant={editor.isActive({ textAlign: 'left' }) ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('left').run()}
        >
          <AlignLeft className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive({ textAlign: 'center' }) ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('center').run()}
        >
          <AlignCenter className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive({ textAlign: 'right' }) ? 'default' : 'ghost'}
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('right').run()}
        >
          <AlignRight className="h-4 w-4" />
        </Button>

        <div className="w-px h-6 bg-gray-300 mx-1" />

        {/* 链接和图片 */}
        <Button
          variant={editor.isActive('link') ? 'default' : 'ghost'}
          size="sm"
          onClick={handleAddLink}
        >
          <LinkIcon className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleImageUpload}
        >
          <ImageIcon className="h-4 w-4" />
        </Button>
      </div>

      {/* 编辑器内容区域 */}
      <EditorContent
        editor={editor}
        className="min-h-[200px]"
      />
    </div>
  )
}
```

### 2. 内容管理 Hook

```typescript
// hooks/useRichTextContent.ts
import { useState, useEffect } from 'react';
import { useDebounce } from './useDebounce';

interface ContentData {
  introduction?: string;
  highlights?: string;
  guide?: string;
  notices?: string;
}

export function useRichTextContent(
  entityType: 'events' | 'venues' | 'circles',
  entityId: string
) {
  const [content, setContent] = useState<ContentData>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // 防抖保存
  const debouncedContent = useDebounce(content, 1000);

  // 加载内容
  useEffect(() => {
    async function loadContent() {
      try {
        const response = await fetch(`/api/${entityType}/${entityId}/content`);
        if (response.ok) {
          const data = await response.json();
          setContent(data);
        }
      } catch (error) {
        console.error('加载内容失败:', error);
      } finally {
        setLoading(false);
      }
    }

    loadContent();
  }, [entityType, entityId]);

  // 自动保存
  useEffect(() => {
    if (
      !loading &&
      debouncedContent &&
      Object.keys(debouncedContent).length > 0
    ) {
      autoSave();
    }
  }, [debouncedContent, loading]);

  // 自动保存函数
  const autoSave = async () => {
    setSaving(true);
    try {
      const response = await fetch(`/api/${entityType}/${entityId}/content`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(content),
      });

      if (response.ok) {
        setLastSaved(new Date());
      }
    } catch (error) {
      console.error('自动保存失败:', error);
    } finally {
      setSaving(false);
    }
  };

  // 手动保存内容
  const saveContent = async (type: keyof ContentData, value: string) => {
    setSaving(true);
    try {
      const response = await fetch(`/api/${entityType}/${entityId}/content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type, content: value }),
      });

      if (!response.ok) throw new Error('保存失败');

      setContent((prev) => ({ ...prev, [type]: value }));
      setLastSaved(new Date());
      return true;
    } catch (error) {
      console.error('保存失败:', error);
      return false;
    } finally {
      setSaving(false);
    }
  };

  return {
    content,
    loading,
    saving,
    lastSaved,
    saveContent,
    updateContent: (type: keyof ContentData, value: string) => {
      setContent((prev) => ({ ...prev, [type]: value }));
    },
  };
}

// hooks/useDebounce.ts
import { useState, useEffect } from 'react';

export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}
```

### 3. 编辑页面组件

```typescript
// components/EventContentEditor.tsx
import React, { useState } from 'react'
import RichTextEditor from './RichTextEditor'
import { useRichTextContent } from '../hooks/useRichTextContent'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Save, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface EventContentEditorProps {
  eventId: string
}

export default function EventContentEditor({ eventId }: EventContentEditorProps) {
  const { content, loading, saving, lastSaved, saveContent, updateContent } =
    useRichTextContent('events', eventId)

  const [activeTab, setActiveTab] = useState<'introduction' | 'highlights' | 'guide' | 'notices'>('introduction')

  const handleManualSave = async () => {
    const success = await saveContent(activeTab, content[activeTab] || '')
    if (success) {
      // 可以添加 toast 通知
      console.log('保存成功')
    } else {
      console.error('保存失败')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  const tabs = [
    { key: 'introduction', label: '展会介绍', description: '展会背景、主题、特色和时间信息' },
    { key: 'highlights', label: '亮点推荐', description: '重点社团、必看作品推荐' },
    { key: 'guide', label: '参观指南', description: '最佳路线、时间安排建议' },
    { key: 'notices', label: '注意事项', description: '规则、禁止事项、安全提醒' }
  ]

  return (
    <div className="max-w-6xl mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl">展会内容编辑</CardTitle>
            <div className="flex items-center gap-4">
              {/* 保存状态 */}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                {saving && (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    <span>保存中...</span>
                  </>
                )}
                {lastSaved && !saving && (
                  <>
                    <Clock className="h-4 w-4" />
                    <span>上次保存: {lastSaved.toLocaleTimeString()}</span>
                  </>
                )}
              </div>

              {/* 手动保存按钮 */}
              <Button
                onClick={handleManualSave}
                disabled={saving}
                size="sm"
              >
                <Save className="h-4 w-4 mr-2" />
                手动保存
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
            <TabsList className="grid w-full grid-cols-4">
              {tabs.map(tab => (
                <TabsTrigger key={tab.key} value={tab.key} className="relative">
                  {tab.label}
                  {content[tab.key as keyof typeof content] && (
                    <Badge variant="secondary" className="ml-2 h-2 w-2 p-0 rounded-full" />
                  )}
                </TabsTrigger>
              ))}
            </TabsList>

            {tabs.map(tab => (
              <TabsContent key={tab.key} value={tab.key} className="mt-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold">{tab.label}</h3>
                    <p className="text-sm text-gray-600">{tab.description}</p>
                  </div>

                  <RichTextEditor
                    content={content[tab.key as keyof typeof content] || ''}
                    onChange={(value) => updateContent(tab.key as keyof typeof content, value)}
                    placeholder={`请输入${tab.label}...`}
                    className="min-h-[400px]"
                  />
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
```

## 后端实现

### 1. 内容管理 API

```typescript
// api/events/[id]/content.ts
import { NextApiRequest, NextApiResponse } from 'next';
import DOMPurify from 'isomorphic-dompurify';
import { z } from 'zod';

// 内容验证 Schema
const ContentSchema = z.object({
  type: z.enum(['introduction', 'highlights', 'guide', 'notices']),
  content: z.string().max(50000), // 限制内容长度
});

const BatchContentSchema = z.object({
  introduction: z.string().optional(),
  highlights: z.string().optional(),
  guide: z.string().optional(),
  notices: z.string().optional(),
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { id } = req.query;

  if (req.method === 'GET') {
    // 获取内容
    try {
      const content = await getEventContent(id as string);
      res.status(200).json(content);
    } catch (error) {
      res.status(500).json({ error: '获取内容失败' });
    }
  } else if (req.method === 'POST') {
    // 保存单个内容
    try {
      const validatedData = ContentSchema.parse(req.body);
      const { type, content } = validatedData;

      // HTML 内容清理 - 针对 Tiptap 输出优化
      const cleanContent = DOMPurify.sanitize(content, {
        ALLOWED_TAGS: [
          'p',
          'br',
          'strong',
          'em',
          'u',
          's',
          'mark',
          'h1',
          'h2',
          'h3',
          'h4',
          'h5',
          'h6',
          'ul',
          'ol',
          'li',
          'a',
          'img',
          'blockquote',
          'code',
          'pre',
          'table',
          'thead',
          'tbody',
          'tr',
          'th',
          'td',
        ],
        ALLOWED_ATTR: [
          'href',
          'src',
          'alt',
          'title',
          'target',
          'rel',
          'class',
          'style',
          'data-*',
          'colspan',
          'rowspan',
        ],
        ALLOWED_URI_REGEXP:
          /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|data):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
      });

      await saveEventContent(id as string, type, cleanContent);
      res.status(200).json({ success: true });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: '数据格式错误', details: error.errors });
      } else {
        res.status(500).json({ error: '保存失败' });
      }
    }
  } else if (req.method === 'PUT') {
    // 批量保存内容（自动保存）
    try {
      const validatedData = BatchContentSchema.parse(req.body);

      // 清理所有内容
      const cleanedContent: Record<string, string> = {};
      for (const [key, value] of Object.entries(validatedData)) {
        if (value) {
          cleanedContent[key] = DOMPurify.sanitize(value, {
            ALLOWED_TAGS: [
              'p',
              'br',
              'strong',
              'em',
              'u',
              's',
              'mark',
              'h1',
              'h2',
              'h3',
              'h4',
              'h5',
              'h6',
              'ul',
              'ol',
              'li',
              'a',
              'img',
              'blockquote',
              'code',
              'pre',
            ],
            ALLOWED_ATTR: [
              'href',
              'src',
              'alt',
              'title',
              'target',
              'class',
              'style',
            ],
          });
        }
      }

      await batchSaveEventContent(id as string, cleanedContent);
      res.status(200).json({ success: true });
    } catch (error) {
      res.status(500).json({ error: '批量保存失败' });
    }
  }
}
```

### 2. 图片上传 API

```typescript
// api/upload/images.ts
import { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';
import { uploadToCloudStorage } from '../../utils/storage';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const form = formidable({
      maxFileSize: 5 * 1024 * 1024, // 5MB
      filter: ({ mimetype }) => mimetype?.startsWith('image/') || false,
    });

    const [fields, files] = await form.parse(req);
    const file = Array.isArray(files.image) ? files.image[0] : files.image;

    if (!file) {
      return res.status(400).json({ error: '没有上传文件' });
    }

    // 上传到云存储
    const url = await uploadToCloudStorage(file);

    res.status(200).json({ url });
  } catch (error) {
    console.error('图片上传失败:', error);
    res.status(500).json({ error: '上传失败' });
  }
}
```

## 数据库设计

```sql
-- 富文本内容表
CREATE TABLE rich_text_contents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  entity_type VARCHAR(20) NOT NULL, -- 'event', 'venue', 'circle'
  entity_id UUID NOT NULL,
  content_type VARCHAR(20) NOT NULL, -- 'introduction', 'highlights', etc.
  content TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  UNIQUE(entity_type, entity_id, content_type)
);

-- 索引
CREATE INDEX idx_rich_text_entity ON rich_text_contents(entity_type, entity_id);
```

## 安全性考虑

### 1. HTML 内容清理

```typescript
import DOMPurify from 'isomorphic-dompurify';

const cleanHTML = DOMPurify.sanitize(dirtyHTML, {
  ALLOWED_TAGS: [
    'p',
    'br',
    'strong',
    'em',
    'u',
    'h1',
    'h2',
    'h3',
    'ul',
    'ol',
    'li',
    'a',
    'img',
  ],
  ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'target'],
  ALLOW_DATA_ATTR: false,
});
```

### 2. 图片上传验证

```typescript
const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
const maxSize = 5 * 1024 * 1024; // 5MB

if (!allowedTypes.includes(file.mimetype)) {
  throw new Error('不支持的文件类型');
}

if (file.size > maxSize) {
  throw new Error('文件大小超出限制');
}
```

## 性能优化

### 1. 编辑器优化

```typescript
// 使用 React.memo 优化组件渲染
const RichTextEditor = React.memo(({ content, onChange, ...props }) => {
  // 编辑器实现
});

// 使用 useMemo 优化扩展配置
const extensions = useMemo(
  () => [
    StarterKit,
    Image,
    Link,
    // ... 其他扩展
  ],
  []
);
```

### 2. 内容缓存和同步

```typescript
// 使用 SWR 进行数据缓存
import useSWR from 'swr';

const { data: content, mutate } = useSWR(
  `/api/events/${eventId}/content`,
  fetcher,
  {
    revalidateOnFocus: false,
    dedupingInterval: 5000, // 5秒内不重复请求
  }
);

// 实时协作（可选）
import { useWebSocket } from './useWebSocket';

const { sendMessage, lastMessage } = useWebSocket(
  `ws://localhost:3000/ws/events/${eventId}`
);

useEffect(() => {
  if (lastMessage) {
    const { type, content } = JSON.parse(lastMessage.data);
    if (type === 'content-update') {
      // 更新编辑器内容
      editor?.commands.setContent(content);
    }
  }
}, [lastMessage, editor]);
```

### 3. 移动端优化

```typescript
// 检测移动设备
const isMobile = useMediaQuery('(max-width: 768px)');

// 移动端简化工具栏
const mobileToolbar = ['bold', 'italic', 'bulletList', 'orderedList', 'link'];

const desktopToolbar = [
  'heading1',
  'heading2',
  'heading3',
  'bold',
  'italic',
  'underline',
  'bulletList',
  'orderedList',
  'textAlign',
  'link',
  'image',
  'color',
];
```

## Tiptap 优势总结

### 1. **现代化架构**

- 基于 ProseMirror，性能优秀
- 完全无头设计，UI 完全可控
- TypeScript 原生支持

### 2. **高度可定制**

- 模块化扩展系统
- 自定义命令和快捷键
- 灵活的样式控制

### 3. **开发体验**

- React Hooks 友好
- 丰富的 API 和事件
- 活跃的社区和文档

### 4. **扩展性**

- 支持协作编辑
- 可以轻松添加自定义功能
- 与现代前端框架深度集成

这个基于 Tiptap 的方案提供了更现代、更灵活的富文本编辑解决方案，特别适合需要高度定制化的项目需求。
