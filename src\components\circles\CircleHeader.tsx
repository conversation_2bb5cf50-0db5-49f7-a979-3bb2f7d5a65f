import React from 'react'
import Image from 'next/image'
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"

interface Circle {
  id: string
  name: string
  urls?: string | null
  created_at?: string
  updated_at?: string
}

export default function CircleHeader({ circle }: { circle: Circle | null }) {
  // 解析 URLs JSON
  const parsedUrls = React.useMemo<Record<string, string>>(() => {
    if (!circle?.urls) return {};
    try {
      return JSON.parse(circle.urls);
    } catch (_) {
      return {};
    }
  }, [circle?.urls]);

  if (!circle) {
    return (
      <header className="w-full bg-background py-8 px-4 border-b">
        <div className="max-w-7xl mx-auto">
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </header>
    );
  }

  return (
    <header className="w-full bg-background py-8 px-4 border-b">
      <div className="max-w-7xl mx-auto grid gap-8 items-start md:grid-cols-12">
        {/* 社团 Logo */}
        <div className="md:col-span-3 lg:col-span-4 flex justify-center">
          <Image
            src={parsedUrls.logo_url || "/globe.svg"}
            alt={`${circle.name} logo`}
            width={200}
            height={200}
            className="rounded-full object-cover shadow-md w-32 h-32 md:w-48 md:h-48"
          />
        </div>

        {/* 社团信息 */}
        <div className="md:col-span-5 lg:col-span-4 flex flex-col gap-6">
          <h1 className="text-3xl md:text-4xl font-bold leading-tight">
            {circle.name}
          </h1>

          {/* 链接列表 */}
          {Object.keys(parsedUrls).length > 0 && (
            <div className="flex flex-col gap-2">
              <h3 className="text-lg font-semibold">相关链接</h3>
              <div className="flex flex-wrap gap-2">
                {Object.entries(parsedUrls).map(([key, url]) => (
                  <a
                    key={key}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary underline text-sm hover:opacity-80"
                  >
                    {key}
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 右侧占位 */}
        <div className="md:col-span-4 lg:col-span-4" />
      </div>
    </header>
  )
} 