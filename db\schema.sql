-- ----------------------------------------
-- Ayafeed API 初始 Schema
-- 包含 circles、artists、events（含场馆信息）、
-- users、logs、appearances 表及基础数据。
-- ----------------------------------------

-- ---------- CORE TABLES ------------------
PRAGMA foreign_keys=OFF;

-- 社团表
DROP TABLE IF EXISTS circles;
CREATE TABLE circles (
  id TEXT PRIMARY KEY,                     -- uuid
  name TEXT NOT NULL UNIQUE,               -- 社团名称，唯一
  urls TEXT CHECK (json_valid(urls)),      -- 社交平台链接（JSON 对象）
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);

-- 作者表
DROP TABLE IF EXISTS artists;
CREATE TABLE artists (
  id TEXT PRIMARY KEY,                     -- uuid
  name TEXT NOT NULL UNIQUE,               -- 作者笔名，唯一
  urls TEXT CHECK (json_valid(urls)),      -- 社交平台链接（JSON 对象）
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);

-- 场馆表
DROP TABLE IF EXISTS venues;
CREATE TABLE venues (
  id TEXT PRIMARY KEY,                    -- venue唯一标识 (如: tokyo-big-sight)

  -- 多语言：场馆名称
  name_en TEXT NOT NULL,
  name_ja TEXT NOT NULL,
  name_zh TEXT NOT NULL,

  -- 多语言：场馆地址
  address_en TEXT,
  address_ja TEXT,
  address_zh TEXT,

  -- 地理位置（必填）
  lat REAL NOT NULL,
  lng REAL NOT NULL,

  -- 场馆基本信息
  capacity INTEGER,                       -- 容量
  website_url TEXT,                       -- 官方网站
  phone TEXT,                            -- 联系电话

  -- 多语言：场馆描述
  description_en TEXT,
  description_ja TEXT,
  description_zh TEXT,

  -- JSON字段：设施信息
  facilities TEXT CHECK (json_valid(facilities)),

  -- JSON字段：交通信息
  transportation TEXT CHECK (json_valid(transportation)),

  -- JSON字段：停车信息
  parking_info TEXT CHECK (json_valid(parking_info)),

  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);

-- 展会表
DROP TABLE IF EXISTS events;
CREATE TABLE events (
  id TEXT PRIMARY KEY,         -- uuid
  -- 多语言：展会名称
  name_en TEXT NOT NULL,
  name_ja TEXT NOT NULL,
  name_zh TEXT NOT NULL,
  -- 多语言：展会日期（字符串展示形式）
  date_en TEXT NOT NULL,
  date_ja TEXT NOT NULL,
  date_zh TEXT NOT NULL,
  date_sort INTEGER NOT NULL,     -- 排序用日期 (yyyymmdd 数值)
  image_url TEXT,              -- 展会图片路径

  -- 场馆关联（外键）
  venue_id TEXT NOT NULL REFERENCES venues(id),

  url TEXT,                    -- 官方网站
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);

-- 图片表
DROP TABLE IF EXISTS images;
CREATE TABLE images (
  id TEXT PRIMARY KEY,                     -- uuid
  group_id TEXT NOT NULL,                  -- 同一组图片的标识
  resource_type TEXT NOT NULL,             -- 'event', 'circle', 'venue'
  resource_id TEXT NOT NULL,               -- 关联的资源ID
  image_type TEXT NOT NULL,                -- 'poster', 'logo', 'banner', 'gallery'
  variant TEXT NOT NULL,                   -- 'original', 'large', 'medium', 'thumb'
  file_path TEXT NOT NULL,                 -- 相对路径，如 "/images/events/reitaisai-22/poster_thumb.jpg"
  file_size INTEGER,                       -- 文件大小（字节）
  width INTEGER,                           -- 图片宽度
  height INTEGER,                          -- 图片高度
  format TEXT,                             -- 文件格式，如 'jpeg', 'png', 'webp'
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),

  -- 索引和约束
  UNIQUE(group_id, variant),               -- 同一组图片的每个变体只能有一个
  CHECK(resource_type IN ('event', 'circle', 'venue')),
  CHECK(image_type IN ('poster', 'logo', 'banner', 'gallery')),
  CHECK(variant IN ('original', 'large', 'medium', 'thumb'))
);

-- 用户表
DROP TABLE IF EXISTS auth_user;
CREATE TABLE auth_user (
	id TEXT NOT NULL PRIMARY KEY,
	username TEXT NOT NULL UNIQUE,
	role TEXT NOT NULL DEFAULT 'user', -- 'user' or 'admin'
	locale TEXT NOT NULL DEFAULT 'en' CHECK (locale IN ('en','ja','zh'))
);

DROP TABLE IF EXISTS auth_session;
CREATE TABLE auth_session (
	id TEXT NOT NULL PRIMARY KEY,
	expires_at TEXT NOT NULL,
	user_id TEXT NOT NULL,
	FOREIGN KEY (user_id) REFERENCES auth_user(id)
);

DROP TABLE IF EXISTS auth_key;
CREATE TABLE auth_key (
	id TEXT NOT NULL PRIMARY KEY,
	user_id TEXT NOT NULL,
	hashed_password TEXT,
	FOREIGN KEY (user_id) REFERENCES auth_user(id)
);

-- 审计日志表
DROP TABLE IF EXISTS logs;
CREATE TABLE logs (
  id TEXT PRIMARY KEY,               -- uuid
  user_id TEXT NOT NULL,             -- 操作者 UID
  username TEXT NOT NULL,            -- 操作者用户名
  action TEXT NOT NULL,              -- 动作，例如 CREATE_EVENT / DELETE_CIRCLE
  target_type TEXT,                  -- 目标实体类型 e.g. event / circle
  target_id TEXT,                    -- 目标实体 ID
  meta TEXT CHECK (json_valid(meta)),-- 额外信息 JSON
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);

-- 参展记录表
DROP TABLE IF EXISTS appearances;
CREATE TABLE appearances (
  id TEXT PRIMARY KEY,         -- uuid
  circle_id TEXT NOT NULL,     -- 社团ID
  event_id TEXT NOT NULL,      -- 展会ID
  artist_id TEXT,              -- 作者ID（可选）
  booth_id TEXT NOT NULL,               -- 摊位编号
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  FOREIGN KEY (circle_id) REFERENCES circles(id) ON DELETE CASCADE,
  FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
  FOREIGN KEY (artist_id) REFERENCES artists(id) ON DELETE SET NULL
);

-- 新增：收藏表（bookmarks）
DROP TABLE IF EXISTS bookmarks;
CREATE TABLE bookmarks (
  id TEXT PRIMARY KEY,                     -- uuid
  user_id TEXT NOT NULL,                   -- 收藏人 UID
  circle_id TEXT NOT NULL,                 -- 被收藏的社团 ID
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  FOREIGN KEY (user_id) REFERENCES auth_user(id) ON DELETE CASCADE,
  FOREIGN KEY (circle_id) REFERENCES circles(id) ON DELETE CASCADE,
  UNIQUE (user_id, circle_id)              -- 防止重复收藏
);

-- 富文本内容表
DROP TABLE IF EXISTS rich_text_contents;
CREATE TABLE rich_text_contents (
  id TEXT PRIMARY KEY,                     -- uuid
  entity_type TEXT NOT NULL,               -- 'event', 'venue', 'circle'
  entity_id TEXT NOT NULL,                 -- 关联的实体ID
  content_type TEXT NOT NULL,              -- 'introduction', 'highlights', 'guide', 'notices'
  content TEXT NOT NULL,                   -- 富文本HTML内容
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),

  -- 约束：同一实体的同一内容类型只能有一条记录
  UNIQUE(entity_type, entity_id, content_type),

  -- 检查约束
  CHECK(entity_type IN ('event', 'venue', 'circle')),
  CHECK(content_type IN ('introduction', 'highlights', 'guide', 'notices'))
);

-- 索引
-- 这两行的意思是：为 bookmarks 表的 user_id 和 circle_id 字段分别创建索引，以加快按用户或社团查询收藏的速度。
CREATE INDEX IF NOT EXISTS idx_bookmarks_user ON bookmarks(user_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_circle ON bookmarks(circle_id);

-- 图片查询优化索引
-- 优化图片查询性能：按资源类型、资源ID、图片类型、变体查询
CREATE INDEX IF NOT EXISTS idx_images_resource_lookup
ON images(resource_type, resource_id, image_type, variant);

-- 优化批量查询性能：按资源类型、资源ID、变体查询
CREATE INDEX IF NOT EXISTS idx_images_resource_variant
ON images(resource_type, resource_id, variant);

-- 优化按组查询：按组ID、变体查询
CREATE INDEX IF NOT EXISTS idx_images_group_variant
ON images(group_id, variant);

-- 富文本内容表索引
-- 按实体类型和ID查询内容
CREATE INDEX IF NOT EXISTS idx_rich_text_entity
ON rich_text_contents(entity_type, entity_id);

-- 按实体类型、ID和内容类型查询特定内容
CREATE INDEX IF NOT EXISTS idx_rich_text_entity_content
ON rich_text_contents(entity_type, entity_id, content_type);

-- 按更新时间查询最近修改的内容
CREATE INDEX IF NOT EXISTS idx_rich_text_updated
ON rich_text_contents(updated_at);

PRAGMA foreign_keys=ON; 

-- 约束提醒：手动更新 updated_at 示例
-- UPDATE artists SET name = ?, updated_at = datetime('now') WHERE id = ?;

-- 本文件可通过以下命令导入 Cloudflare D1（SQLite）数据库：
-- wrangler d1 execute <DB_NAME> --file=db/schema.sql 