'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Bookmark, 
  Calendar, 
  Users, 
  MapPin, 
  Clock, 
  Filter,
  Grid3X3,
  List,
  Search,
  Heart,
  ExternalLink,
  Trash2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Link from 'next/link';

interface BookmarkItem {
  id: string;
  type: 'event' | 'circle';
  title: string;
  description: string;
  date?: string;
  location?: string;
  image?: string;
  tags: string[];
  bookmarkedAt: string;
}

export function UserBookmarks() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('recent');
  const [filterType, setFilterType] = useState<'all' | 'event' | 'circle'>('all');

  // Mock data - 在实际应用中这些数据会从API获取
  const bookmarks: BookmarkItem[] = [
    {
      id: '1',
      type: 'event',
      title: 'Reitaisai 22',
      description: '东方Project同人展会，汇聚众多优秀的同人作品和创作者',
      date: '2024-05-26',
      location: '东京国际展示场',
      tags: ['同人展', '东方Project', '音乐'],
      bookmarkedAt: '2024-01-15T10:30:00Z',
    },
    {
      id: '2',
      type: 'circle',
      title: 'Team Shanghai Alice',
      description: 'ZUN创立的同人游戏制作团体，东方Project系列的原作者',
      tags: ['游戏制作', '音乐创作', '原作'],
      bookmarkedAt: '2024-01-10T14:20:00Z',
    },
    {
      id: '3',
      type: 'event',
      title: 'Comic Market 105',
      description: '世界最大规模的同人志即卖会',
      date: '2024-12-30',
      location: '东京国际展示场',
      tags: ['同人志', 'Comiket', '创作'],
      bookmarkedAt: '2024-01-08T09:15:00Z',
    },
    {
      id: '4',
      type: 'circle',
      title: 'IOSYS',
      description: '知名同人音乐制作团体，以电子音乐和Vocal作品著称',
      tags: ['音乐制作', '电子音乐', 'Vocal'],
      bookmarkedAt: '2024-01-05T16:45:00Z',
    },
  ];

  const filteredBookmarks = bookmarks
    .filter(item => {
      if (filterType !== 'all' && item.type !== filterType) return false;
      if (searchQuery && !item.title.toLowerCase().includes(searchQuery.toLowerCase())) return false;
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          return new Date(b.bookmarkedAt).getTime() - new Date(a.bookmarkedAt).getTime();
        case 'oldest':
          return new Date(a.bookmarkedAt).getTime() - new Date(b.bookmarkedAt).getTime();
        case 'name':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'event':
        return Calendar;
      case 'circle':
        return Users;
      default:
        return Bookmark;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'event':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'circle':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const BookmarkCard = ({ item, index }: { item: BookmarkItem; index: number }) => {
    const TypeIcon = getTypeIcon(item.type);
    
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.05 }}
        layout
      >
        <Card className="group hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800 border-slate-200/50 dark:border-slate-700/50">
          <CardContent className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${getTypeColor(item.type)}`}>
                  <TypeIcon className="h-4 w-4" />
                </div>
                <Badge variant="secondary" className={getTypeColor(item.type)}>
                  {item.type === 'event' ? '事件' : '社团'}
                </Badge>
              </div>
              <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/${item.type}s/${item.id}`}>
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <h3 className="font-semibold text-lg text-slate-900 dark:text-slate-100 mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              {item.title}
            </h3>
            
            <p className="text-slate-600 dark:text-slate-400 text-sm mb-4 line-clamp-2">
              {item.description}
            </p>
            
            {item.date && (
              <div className="flex items-center gap-2 text-sm text-slate-500 mb-2">
                <Calendar className="h-4 w-4" />
                {formatDate(item.date)}
              </div>
            )}
            
            {item.location && (
              <div className="flex items-center gap-2 text-sm text-slate-500 mb-4">
                <MapPin className="h-4 w-4" />
                {item.location}
              </div>
            )}
            
            <div className="flex flex-wrap gap-2 mb-4">
              {item.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {item.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{item.tags.length - 3}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center justify-between text-xs text-slate-400">
              <div className="flex items-center gap-1">
                <Heart className="h-3 w-3" />
                收藏于 {formatDate(item.bookmarkedAt)}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
              我的收藏
            </h1>
            <p className="text-slate-600 dark:text-slate-400 mt-1">
              管理您收藏的事件和社团
            </p>
          </div>
          <Badge variant="secondary" className="text-sm">
            {filteredBookmarks.length} 项收藏
          </Badge>
        </div>
      </motion.div>

      {/* Filters and Controls */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card className="bg-gradient-to-r from-white to-slate-50 dark:from-slate-900 dark:to-slate-800 border-slate-200/50 dark:border-slate-700/50">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    placeholder="搜索收藏..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              {/* Filters */}
              <div className="flex gap-3">
                <Select value={filterType} onValueChange={(value: any) => setFilterType(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="event">事件</SelectItem>
                    <SelectItem value="circle">社团</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="recent">最新收藏</SelectItem>
                    <SelectItem value="oldest">最早收藏</SelectItem>
                    <SelectItem value="name">按名称</SelectItem>
                  </SelectContent>
                </Select>
                
                <div className="flex border rounded-lg">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Bookmarks Grid/List */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <AnimatePresence mode="wait">
          {filteredBookmarks.length > 0 ? (
            <motion.div
              key={viewMode}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                  : 'space-y-4'
              }
            >
              {filteredBookmarks.map((item, index) => (
                <BookmarkCard key={item.id} item={item} index={index} />
              ))}
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-12"
            >
              <Bookmark className="h-16 w-16 text-slate-300 dark:text-slate-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
                暂无收藏
              </h3>
              <p className="text-slate-600 dark:text-slate-400">
                {searchQuery || filterType !== 'all' 
                  ? '没有找到匹配的收藏项目' 
                  : '开始收藏您感兴趣的事件和社团吧'}
              </p>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
}
