'use client';

import React from 'react';
import { Heart, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useToggleBookmark, useBookmarkStatus, validateCircleId } from '@/hooks/useBookmark';
import { toast } from 'sonner';

interface BookmarkButtonProps {
  circleId: string;
  size?: 'sm' | 'default' | 'lg' | 'icon';
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  showText?: boolean;
  className?: string;
  disabled?: boolean;
  /** 已知的收藏状态，如果提供则不会查询 API */
  knownStatus?: {
    isBookmarked: boolean;
    bookmarkId?: string | null;
  };
}

/**
 * BookmarkButton 收藏按钮组件
 * 
 * 功能特性：
 * - 显示当前收藏状态
 * - 支持切换收藏状态
 * - 乐观更新UI
 * - 错误处理和用户反馈
 * - 多种尺寸和样式
 * - 可选文本显示
 */
export function BookmarkButton({
  circleId,
  size = 'default',
  variant = 'outline',
  showText = true,
  className,
  disabled = false,
  knownStatus,
}: BookmarkButtonProps) {
  // 验证 circleId
  if (!validateCircleId(circleId)) {
    console.warn('BookmarkButton: Invalid circleId provided:', circleId);
    return null;
  }

  // 获取收藏状态
  const { 
    data: status, 
    isLoading: statusLoading, 
    error: statusError 
  } = useBookmarkStatus(circleId);

  // 切换收藏状态
  const toggleMutation = useToggleBookmark();

  const handleToggle = async () => {
    if (disabled || toggleMutation.isPending) return;

    try {
      await toggleMutation.mutateAsync({
        pathParams: { circleId }
      });
    } catch (error) {
      // 错误处理已在 hook 中完成，这里不需要额外处理
      console.error('Bookmark toggle failed:', error);
    }
  };

  // 计算按钮状态
  const isBookmarked = status?.data?.isBookmarked ?? false;
  const isLoading = statusLoading || toggleMutation.isPending;
  const isDisabled = disabled || isLoading || !!statusError;

  // 获取显示文本
  const getButtonText = () => {
    if (toggleMutation.isPending) {
      return 'Processing...';
    }
    if (statusLoading) {
      return 'Loading...';
    }
    if (statusError) {
      return 'Error';
    }
    return isBookmarked ? 'Bookmarked' : 'Bookmark';
  };

  // 获取图标样式
  const getHeartIconClass = () => {
    const baseClass = 'transition-colors duration-200';
    
    if (isBookmarked) {
      return cn(
        baseClass,
        'fill-red-500 text-red-500',
        showText && 'mr-2'
      );
    }
    
    return cn(
      baseClass,
      'text-muted-foreground hover:text-red-500',
      showText && 'mr-2'
    );
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleToggle}
      disabled={isDisabled}
      className={cn(
        'transition-all duration-200',
        isBookmarked && variant === 'outline' && 'border-red-200 bg-red-50 hover:bg-red-100',
        className
      )}
      aria-label={`${isBookmarked ? 'Remove from' : 'Add to'} bookmarks`}
    >
      {isLoading ? (
        <Loader2 className={cn('animate-spin', showText && 'mr-2')} />
      ) : (
        <Heart className={getHeartIconClass()} />
      )}
      
      {showText && (
        <span className="font-medium">
          {getButtonText()}
        </span>
      )}
    </Button>
  );
}

/**
 * BookmarkIconButton 纯图标收藏按钮
 * 适用于空间受限的场景
 */
export function BookmarkIconButton({
  circleId,
  size = 'icon',
  variant = 'ghost',
  className,
  disabled = false,
}: Omit<BookmarkButtonProps, 'showText'>) {
  return (
    <BookmarkButton
      circleId={circleId}
      size={size}
      variant={variant}
      showText={false}
      className={className}
      disabled={disabled}
    />
  );
}

/**
 * BookmarkCompactButton 紧凑型收藏按钮
 * 适用于列表项等紧凑布局
 */
export function BookmarkCompactButton({
  circleId,
  className,
  disabled = false,
}: Pick<BookmarkButtonProps, 'circleId' | 'className' | 'disabled'>) {
  return (
    <BookmarkButton
      circleId={circleId}
      size="sm"
      variant="ghost"
      showText={false}
      className={cn('h-8 w-8 p-0', className)}
      disabled={disabled}
    />
  );
}

export default BookmarkButton;
