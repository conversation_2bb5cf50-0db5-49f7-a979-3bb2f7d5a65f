# 收藏模块前端对接文档

> 📝 **版本**: v1.0.0  
> 🕒 **更新时间**: 2025-01-02  
> 👥 **目标读者**: 前端开发者

## 📋 快速导航

- [🚀 快速开始](#-快速开始)
- [🔌 API 接口](#-api-接口)
- [💻 前端集成](#-前端集成)
- [🎨 UI 组件示例](#-ui-组件示例)
- [🛡️ 安全注意事项](#️-安全注意事项)
- [❓ 常见问题](#-常见问题)

## 🚀 快速开始

### 基础信息

**模块功能**: 为用户提供社团收藏功能  
**基础路径**: `/circles/{circleId}/bookmark`  
**支持操作**: 切换收藏状态（收藏/取消收藏）  
**认证要求**: 所有操作都需要用户登录

### 5分钟上手

```typescript
// 1. 切换收藏状态
const toggleMutation = useToggleBookmark();
toggleMutation.mutate('circle-123');

// 2. 在组件中使用
function BookmarkButton({ circleId }: { circleId: string }) {
  const { data: isBookmarked, isLoading } = useBookmarkStatus(circleId);
  const toggleMutation = useToggleBookmark();

  const handleToggle = () => {
    toggleMutation.mutate(circleId);
  };

  return (
    <button 
      onClick={handleToggle}
      disabled={isLoading || toggleMutation.isPending}
    >
      {isBookmarked ? '已收藏' : '收藏'}
    </button>
  );
}
```

## 🔌 API 接口

### 接口概览

| 方法 | 路径 | 功能 | 权限 |
|------|------|------|------|
| `POST` | `/circles/{circleId}/bookmark` | 切换收藏状态 | 登录用户 |

### 数据类型定义

```typescript
// 收藏对象
interface Bookmark {
  id: string;
  user_id: string;
  circle_id: string;
  created_at: string;
  updated_at: string;
}

// 切换收藏响应
interface ToggleBookmarkResponse {
  isBookmarked: boolean;
}

// API 响应格式
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}
```

### 详细接口说明

#### 切换收藏状态

```typescript
POST /circles/{circleId}/bookmark

// 路径参数
{
  circleId: string; // 社团ID，如 'circle-123'
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例
{
  "code": 0,
  "message": "已加入收藏", // 或 "已取消收藏"
  "data": {
    "isBookmarked": true // 当前收藏状态
  }
}
```

**功能说明**:
- 如果用户未收藏该社团，则添加收藏，返回 `isBookmarked: true`
- 如果用户已收藏该社团，则取消收藏，返回 `isBookmarked: false`
- 操作是原子性的，不会出现重复收藏的情况

## 💻 前端集成

### React Query Hooks

```typescript
// hooks/useBookmark.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { request } from '@/lib/http';

// 切换收藏状态
export function useToggleBookmark() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (circleId: string) => {
      return request<ToggleBookmarkResponse>(`/circles/${circleId}/bookmark`, {
        method: 'POST',
      });
    },
    onSuccess: (data, circleId) => {
      // 更新收藏状态缓存
      queryClient.setQueryData(['bookmark-status', circleId], data.isBookmarked);
      
      // 可选：更新社团列表中的收藏状态
      queryClient.invalidateQueries({
        queryKey: ['circles'],
      });
    },
    onError: (error) => {
      console.error('Toggle bookmark failed:', error);
    },
  });
}

// 获取收藏状态（通过本地状态管理）
export function useBookmarkStatus(circleId: string) {
  return useQuery({
    queryKey: ['bookmark-status', circleId],
    queryFn: () => {
      // 这里可以从本地存储或其他方式获取状态
      // 由于后端只提供 toggle 接口，我们需要在前端维护状态
      const bookmarks = getLocalBookmarks();
      return bookmarks.includes(circleId);
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    enabled: !!circleId,
  });
}

// 本地收藏状态管理
function getLocalBookmarks(): string[] {
  try {
    const stored = localStorage.getItem('user-bookmarks');
    return stored ? JSON.parse(stored) : [];
  } catch {
    return [];
  }
}

function setLocalBookmarks(bookmarks: string[]) {
  try {
    localStorage.setItem('user-bookmarks', JSON.stringify(bookmarks));
  } catch (error) {
    console.error('Failed to save bookmarks to localStorage:', error);
  }
}

// 更新本地收藏状态
export function updateLocalBookmarkStatus(circleId: string, isBookmarked: boolean) {
  const bookmarks = getLocalBookmarks();
  
  if (isBookmarked && !bookmarks.includes(circleId)) {
    bookmarks.push(circleId);
  } else if (!isBookmarked && bookmarks.includes(circleId)) {
    const index = bookmarks.indexOf(circleId);
    bookmarks.splice(index, 1);
  }
  
  setLocalBookmarks(bookmarks);
}

// 增强版切换收藏 Hook
export function useToggleBookmarkEnhanced() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (circleId: string) => {
      return request<ToggleBookmarkResponse>(`/circles/${circleId}/bookmark`, {
        method: 'POST',
      });
    },
    onSuccess: (data, circleId) => {
      // 更新本地存储
      updateLocalBookmarkStatus(circleId, data.isBookmarked);
      
      // 更新查询缓存
      queryClient.setQueryData(['bookmark-status', circleId], data.isBookmarked);
      
      // 可选：更新相关查询
      queryClient.invalidateQueries({
        queryKey: ['circles'],
      });
    },
  });
}
```

### 错误处理

```typescript
// utils/bookmarkErrorHandler.ts
export function handleBookmarkError(error: any) {
  if (error?.code === 20001) {
    return '请先登录后再进行收藏操作';
  }
  if (error?.code === 40001) {
    return '社团ID无效';
  }
  if (error?.code === 10002) {
    return '社团不存在';
  }
  return '操作失败，请重试';
}

// 在组件中使用
const toggleMutation = useToggleBookmarkEnhanced();

const handleToggle = async (circleId: string) => {
  try {
    await toggleMutation.mutateAsync(circleId);
    toast.success('操作成功');
  } catch (error) {
    const message = handleBookmarkError(error);
    toast.error(message);
  }
};
```

## 🎨 UI 组件示例

### 基础收藏按钮

```tsx
// components/BookmarkButton.tsx
import { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToggleBookmarkEnhanced } from '@/hooks/useBookmark';
import { toast } from 'sonner';

interface BookmarkButtonProps {
  circleId: string;
  initialBookmarked?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
  showText?: boolean;
  className?: string;
}

export function BookmarkButton({
  circleId,
  initialBookmarked = false,
  size = 'md',
  variant = 'outline',
  showText = true,
  className = '',
}: BookmarkButtonProps) {
  const [isBookmarked, setIsBookmarked] = useState(initialBookmarked);
  const toggleMutation = useToggleBookmarkEnhanced();

  // 从本地存储同步状态
  useEffect(() => {
    const bookmarks = getLocalBookmarks();
    setIsBookmarked(bookmarks.includes(circleId));
  }, [circleId]);

  const handleToggle = async () => {
    try {
      const result = await toggleMutation.mutateAsync(circleId);
      setIsBookmarked(result.isBookmarked);
      
      const message = result.isBookmarked ? '已加入收藏' : '已取消收藏';
      toast.success(message);
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const sizeClasses = {
    sm: 'h-8 px-2 text-sm',
    md: 'h-9 px-3',
    lg: 'h-10 px-4',
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleToggle}
      disabled={toggleMutation.isPending}
      className={`${sizeClasses[size]} ${className}`}
    >
      <Heart 
        className={`w-4 h-4 ${isBookmarked ? 'fill-red-500 text-red-500' : ''} ${
          showText ? 'mr-2' : ''
        }`}
      />
      {showText && (
        <span>
          {toggleMutation.isPending 
            ? '处理中...' 
            : isBookmarked 
              ? '已收藏' 
              : '收藏'
          }
        </span>
      )}
    </Button>
  );
}

// 获取本地收藏状态的辅助函数
function getLocalBookmarks(): string[] {
  try {
    const stored = localStorage.getItem('user-bookmarks');
    return stored ? JSON.parse(stored) : [];
  } catch {
    return [];
  }
}
```

### 收藏状态指示器

```tsx
// components/BookmarkIndicator.tsx
import { Heart } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface BookmarkIndicatorProps {
  isBookmarked: boolean;
  count?: number;
  size?: 'sm' | 'md' | 'lg';
}

export function BookmarkIndicator({
  isBookmarked,
  count,
  size = 'md'
}: BookmarkIndicatorProps) {
  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  return (
    <div className="flex items-center gap-1">
      <Heart
        className={`${sizeClasses[size]} ${
          isBookmarked ? 'fill-red-500 text-red-500' : 'text-gray-400'
        }`}
      />
      {count !== undefined && (
        <Badge variant="secondary" className="text-xs">
          {count}
        </Badge>
      )}
    </div>
  );
}
```

### 社团卡片集成示例

```tsx
// components/CircleCard.tsx
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BookmarkButton } from './BookmarkButton';

interface CircleCardProps {
  circle: {
    id: string;
    name: string;
    category: string;
    description?: string;
  };
  isBookmarked?: boolean;
}

export function CircleCard({ circle, isBookmarked = false }: CircleCardProps) {
  return (
    <Card className="relative">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg">{circle.name}</CardTitle>
          <BookmarkButton
            circleId={circle.id}
            initialBookmarked={isBookmarked}
            size="sm"
            variant="ghost"
            showText={false}
          />
        </div>
        <p className="text-sm text-muted-foreground">
          {circle.category}
        </p>
      </CardHeader>
      {circle.description && (
        <CardContent>
          <p className="text-sm">{circle.description}</p>
        </CardContent>
      )}
    </Card>
  );
}
```

## 🛡️ 安全注意事项

### 认证要求

```typescript
// 确保用户已登录
import { useAuth } from '@/hooks/useAuth';

function BookmarkFeature({ circleId }: { circleId: string }) {
  const { isAuthenticated, user } = useAuth();

  if (!isAuthenticated) {
    return (
      <Button variant="outline" onClick={() => router.push('/login')}>
        登录后收藏
      </Button>
    );
  }

  return <BookmarkButton circleId={circleId} />;
}
```

### 数据验证

```typescript
// 验证 circleId 格式
function validateCircleId(circleId: string): boolean {
  return /^[a-zA-Z0-9-_]+$/.test(circleId) && circleId.length > 0;
}

// 在使用前验证
const handleBookmark = (circleId: string) => {
  if (!validateCircleId(circleId)) {
    toast.error('无效的社团ID');
    return;
  }

  toggleMutation.mutate(circleId);
};
```

### 防抖处理

```typescript
// 防止重复点击
import { useDebouncedCallback } from 'use-debounce';

export function BookmarkButtonWithDebounce({ circleId }: { circleId: string }) {
  const toggleMutation = useToggleBookmarkEnhanced();

  const debouncedToggle = useDebouncedCallback(
    (id: string) => {
      toggleMutation.mutate(id);
    },
    300 // 300ms 防抖
  );

  return (
    <Button
      onClick={() => debouncedToggle(circleId)}
      disabled={toggleMutation.isPending}
    >
      收藏
    </Button>
  );
}
```

## 📊 性能优化

### 批量操作优化

```typescript
// 批量更新收藏状态
export function useBatchBookmarkUpdate() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (operations: Array<{ circleId: string; action: 'add' | 'remove' }>) => {
      // 这里可以实现批量操作的逻辑
      // 目前后端只支持单个操作，所以串行执行
      const results = [];
      for (const op of operations) {
        const result = await request<ToggleBookmarkResponse>(
          `/circles/${op.circleId}/bookmark`,
          { method: 'POST' }
        );
        results.push({ circleId: op.circleId, ...result });
      }
      return results;
    },
    onSuccess: (results) => {
      // 批量更新缓存
      results.forEach(({ circleId, isBookmarked }) => {
        queryClient.setQueryData(['bookmark-status', circleId], isBookmarked);
        updateLocalBookmarkStatus(circleId, isBookmarked);
      });
    },
  });
}
```

### 虚拟化列表支持

```typescript
// 大列表中的收藏状态管理
import { useVirtualizer } from '@tanstack/react-virtual';

export function VirtualizedCircleList({ circles }: { circles: Circle[] }) {
  const parentRef = useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: circles.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 120,
  });

  return (
    <div ref={parentRef} className="h-96 overflow-auto">
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const circle = circles[virtualItem.index];
          return (
            <div
              key={virtualItem.key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualItem.size}px`,
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              <CircleCard circle={circle} />
            </div>
          );
        })}
      </div>
    </div>
  );
}
```

## ❓ 常见问题

### Q: 如何获取用户的收藏列表？

**A**: 当前后端只提供切换收藏状态的接口，没有提供获取收藏列表的接口。建议在前端使用本地存储来维护用户的收藏状态：

```typescript
// 获取用户所有收藏
function getUserBookmarks(): string[] {
  try {
    const stored = localStorage.getItem('user-bookmarks');
    return stored ? JSON.parse(stored) : [];
  } catch {
    return [];
  }
}

// 检查特定社团是否被收藏
function isCircleBookmarked(circleId: string): boolean {
  const bookmarks = getUserBookmarks();
  return bookmarks.includes(circleId);
}
```

### Q: 如何处理用户登录状态变化？

**A**: 监听认证状态变化，清理或同步收藏数据：

```typescript
import { useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';

export function BookmarkSyncProvider({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, user } = useAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      // 用户登出时清理本地收藏数据
      localStorage.removeItem('user-bookmarks');
    } else {
      // 用户登录时可以从服务器同步数据（如果有相关接口）
      // 目前只能依赖本地存储
    }
  }, [isAuthenticated, user?.id]);

  return <>{children}</>;
}
```

### Q: 如何处理网络错误和重试？

**A**: 使用 React Query 的内置重试机制：

```typescript
export function useToggleBookmarkWithRetry() {
  return useMutation({
    mutationFn: async (circleId: string) => {
      return request<ToggleBookmarkResponse>(`/circles/${circleId}/bookmark`, {
        method: 'POST',
      });
    },
    retry: (failureCount, error) => {
      // 网络错误重试，认证错误不重试
      if (error?.code === 20001) return false;
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}
```

### Q: 如何实现收藏数量统计？

**A**: 由于后端没有提供统计接口，需要在前端维护：

```typescript
// 收藏数量管理
export function useBookmarkCount() {
  const [count, setCount] = useState(0);

  useEffect(() => {
    const bookmarks = getUserBookmarks();
    setCount(bookmarks.length);
  }, []);

  const updateCount = useCallback((delta: number) => {
    setCount(prev => Math.max(0, prev + delta));
  }, []);

  return { count, updateCount };
}
```

---

## 📚 相关文档

- [API 规范文档](./api-specification.md)
- [社团模块文档](./circle-module.md)
- [用户认证文档](./auth-module.md)
- [错误处理指南](./error-handling.md)

---

**📞 技术支持**: 如有问题请联系后端团队或查看项目 Issues
