import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import type { Context } from 'hono';

import { toggleCircleBookmark } from '../bookmark/controller';
import { circleSchema } from './schema';
import * as circleService from './service';
import { getDB, Cache, Logger } from '@/infrastructure';
import type { Locale } from '@/middlewares/locale';
import { roleGuard } from '@/middlewares/roleGuard';
import {
  paginatedResult,
  successResponse,
  errorResponse,
} from '@/utils/schemas';
import { HonoApp } from '@/types';
import { jsonError } from '@/utils/errorResponse';
import { jsonWithFields } from '@/utils/fieldFilter';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

// 导入 bookmark 相关功能

// 公共 Circle 只读接口
const pubCircles = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI Routes ----------
const listCirclesRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '社团列表（公开）',
  tags: ['Circles'],
  request: {
    query: z.object({
      page: z.string().optional().openapi({ example: '1' }),
      pageSize: z.string().optional().openapi({ example: '50' }),
      search: z.string().optional().openapi({
        example: '東方',
        description: '搜索关键词，支持社团名称和作者搜索',
      }),
    }),
  },
  responses: {
    200: {
      description: '社团列表',
      content: {
        'application/json': { schema: paginatedResult(circleSchema) },
      },
    },
  },
});

const getCircleRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '社团详情（按 ID）',
  tags: ['Circles'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
  },
  responses: {
    200: {
      description: '社团详情',
      content: { 'application/json': { schema: circleSchema } },
    },
    404: { description: 'Not Found' },
  },
});

// 新增：社团参展历史
const getCircleAppearancesRoute = createRoute({
  method: 'get',
  path: '/{id}/appearances',
  summary: '社团参展历史',
  tags: ['Circles'],
  request: {
    params: z.object({ id: z.string() }),
    query: z.object({
      page: z.string().optional().openapi({ example: '1' }),
      pageSize: z.string().optional().openapi({ example: '50' }),
    }),
  },
  responses: {
    200: {
      description: '参展记录分页',
      content: { 'application/json': { schema: paginatedResult(z.any()) } },
    },
  },
});

// 新增：社团收藏功能
const toggleBookmarkRoute = createRoute({
  method: 'post',
  path: '/{circleId}/bookmark',
  summary: '切换收藏状态',
  tags: ['Bookmarks'],
  request: {
    params: z.object({
      circleId: z.string().openapi({ example: 'circle-uuid' }),
    }),
  },
  responses: {
    200: {
      description: '收藏状态已切换',
      content: {
        'application/json': {
          schema: successResponse.extend({
            data: z.object({ isBookmarked: z.boolean() }),
          }),
        },
      },
    },
    401: {
      description: '未登录',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

// ---------- Handlers ----------
async function listCirclesHandler(c: Context) {
  const db = getDB(c);
  const cache: Cache | undefined = c.get('cache');
  const logger: Logger | undefined = c.get('logger');
  const locale = (c.get('locale') as Locale) || 'en';

  // 查询参数解析
  const search = new URL(c.req.url).searchParams;
  const page = Math.max(Number(search.get('page') || '1'), 1);
  const pageSize = Math.max(Number(search.get('pageSize') || '50'), 1);
  const searchKeyword = search.get('search') || undefined;

  // 使用 Service 获取数据，支持数据库层分页和搜索
  const result = await circleService.listCircles(
    db,
    locale,
    { page, pageSize, search: searchKeyword },
    cache,
    logger
  );

  return jsonWithFields(c, {
    items: result.items,
    total: result.total,
    page,
    pageSize,
  });
}

async function getCircleHandler(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  const circle = await circleService.getCircle(db, id);
  if (!circle) return jsonError(c, 60001, '资源不存在', 404);
  return jsonWithFields(c, circle);
}

// 新增 Handler：社团参展历史
async function getCircleAppearancesHandler(c: Context<HonoApp>) {
  const db = getDB(c);
  const circleId = c.req.param('id');

  const search = new URL(c.req.url).searchParams;
  const page = Math.max(Number(search.get('page') || '1'), 1);
  const pageSize = Math.max(Number(search.get('pageSize') || '50'), 1);
  const offset = (page - 1) * pageSize;

  // total
  const totalRes = await db
    .prepare('SELECT COUNT(*) AS total FROM appearances WHERE circle_id = ?')
    .bind(circleId)
    .first<{ total: number }>();
  const total = totalRes?.total || 0;

  // data
  const { results: items } = await db
    .prepare(
      `
      SELECT a.event_id, e.name AS event_name, e.date AS event_date, a.booth_id
      FROM appearances a
      JOIN events e ON a.event_id = e.id
      WHERE a.circle_id = ?
      ORDER BY e.date_sort DESC
      LIMIT ? OFFSET ?`
    )
    .bind(circleId, pageSize, offset)
    .all();

  return c.json({ items, total, page, pageSize });
}

// ---------- Register ----------
registerOpenApiRoute(pubCircles, listCirclesRoute, listCirclesHandler);
registerOpenApiRoute(pubCircles, getCircleRoute, getCircleHandler);
registerOpenApiRoute(
  pubCircles,
  getCircleAppearancesRoute,
  getCircleAppearancesHandler
);

// 注册需要认证的 bookmark 路由
pubCircles.use('/{circleId}/bookmark', roleGuard());
registerOpenApiRoute(pubCircles, toggleBookmarkRoute, toggleCircleBookmark);

export { pubCircles, pubCircles as routes };
