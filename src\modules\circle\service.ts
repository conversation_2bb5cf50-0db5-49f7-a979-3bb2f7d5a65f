import type { D1Database } from '@cloudflare/workers-types';

import { createCircleRepository } from './repository';
import type { Circle, CircleCreateInput, CircleUpdateInput } from './schema';
import { C<PERSON>, Logger } from '@/infrastructure';
import type { Locale } from '@/middlewares/locale';

/**
 * 列出社团，支持分页和搜索
 */
export async function listCircles(
  db: D1Database,
  locale: Locale,
  options: {
    page?: number;
    pageSize?: number;
    search?: string;
  } = {},
  cache?: Cache,
  logger?: Logger
): Promise<{ items: Circle[]; total: number }> {
  const { page = 1, pageSize = 50, search } = options;
  const offset = (page - 1) * pageSize;

  // 构建缓存键，包含所有查询参数
  const cacheKey = `circles:${locale}:${JSON.stringify({ page, pageSize, search })}`;
  if (cache) {
    const cached = await cache.get<{ items: Circle[]; total: number }>(
      cacheKey
    );
    if (cached) {
      logger?.debug?.('listCircles: hit cache', { key: cacheKey });
      return cached;
    }
  }

  // 构建 WHERE 条件
  const conditions: string[] = [];
  const params: any[] = [locale];

  if (search) {
    conditions.push(`(
      UPPER(COALESCE(ct.name, c.name)) LIKE UPPER(?) OR
      UPPER(json_extract(c.urls, '$.author')) LIKE UPPER(?)
    )`);
    params.push(`%${search}%`, `%${search}%`);
  }

  const whereClause =
    conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  // 查询总数
  const countQuery = `
    SELECT COUNT(*) as total
    FROM circles c
    LEFT JOIN circle_translations ct ON c.id = ct.circle_id AND ct.locale = ?
    ${whereClause}
  `;

  const totalResult = await db
    .prepare(countQuery)
    .bind(...params)
    .first<{ total: number }>();
  const total = totalResult?.total || 0;

  // 查询数据
  const dataQuery = `
    SELECT
      c.id,
      COALESCE(ct.name, c.name) as name,
      c.urls,
      c.created_at,
      c.updated_at
    FROM circles c
    LEFT JOIN circle_translations ct ON c.id = ct.circle_id AND ct.locale = ?
    ${whereClause}
    ORDER BY COALESCE(ct.name, c.name) ASC
    LIMIT ? OFFSET ?
  `;

  const { results } = await db
    .prepare(dataQuery)
    .bind(...params, pageSize, offset)
    .all();
  const items = results as Circle[];

  const result = { items, total };

  if (cache) {
    // 缓存时间根据是否有搜索条件调整
    const cacheTime = search ? 60 : 300; // 搜索结果缓存时间更短
    await cache.set(cacheKey, result, cacheTime);
    logger?.debug?.('listCircles: cached results', {
      key: cacheKey,
      count: items.length,
      total,
    });
  }

  return result;
}

/**
 * 创建社团
 */
export async function createCircle(
  db: D1Database,
  input: CircleCreateInput
): Promise<Circle> {
  const repo = createCircleRepository(db);
  return repo.create(input);
}

/**
 * 获取社团详情
 */
export async function getCircle(
  db: D1Database,
  id: string
): Promise<Circle | null> {
  const repo = createCircleRepository(db);
  return repo.findById(id);
}

/**
 * 更新社团
 */
export async function updateCircle(
  db: D1Database,
  id: string,
  input: CircleUpdateInput
): Promise<Circle | null> {
  const repo = createCircleRepository(db);
  return repo.update(id, input);
}

/**
 * 删除社团
 */
export async function deleteCircle(db: D1Database, id: string): Promise<void> {
  const repo = createCircleRepository(db);
  await repo.delete(id);
}
