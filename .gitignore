# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (ignore all real env files, but keep example)
.env*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
/docs/next-project.md

# interview documents (personal use only)
面试问题与详细回答.md

# docusaurus
/website/.docusaurus/
/website/build/
/website/node_modules/
/website/.cache/
/website/yarn-error.log
/website/npm-debug.log*
/website/yarn-debug.log*
/website/yarn-error.log*
/website/.pnpm-debug.log*
/website/.env.local
/website/.env.development.local
/website/.env.test.local
/website/.env.production.local
面试案例_Toast问题调试.md
