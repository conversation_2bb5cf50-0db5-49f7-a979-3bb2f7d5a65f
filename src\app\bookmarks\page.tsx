'use client';

import React from 'react';
import { BookmarkList, BookmarkStats } from '@/components/bookmark';

export default function BookmarksPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* 页面标题 */}
        <header className="mb-8 text-center space-y-4">
          <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
            My Bookmarks
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
            Manage and explore your bookmarked circles
          </p>
        </header>

        {/* 统计信息 */}
        <div className="mb-8">
          <BookmarkStats variant="compact" showTitle={false} />
        </div>

        {/* 收藏列表 */}
        <main>
          <BookmarkList showHeader={false} />
        </main>
      </div>
    </div>
  );
}
