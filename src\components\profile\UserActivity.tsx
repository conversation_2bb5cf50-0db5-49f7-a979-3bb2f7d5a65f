'use client';

import React, { useState } from 'react';
import { motion } from 'motion/react';
import { 
  Activity, 
  Eye, 
  Bookmark, 
  Search, 
  Calendar,
  Clock,
  Filter,
  TrendingUp,
  Bar<PERSON><PERSON><PERSON>,
  <PERSON>Pointer,
  Heart,
  Share2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';

interface ActivityItem {
  id: string;
  type: 'view' | 'bookmark' | 'search' | 'share';
  title: string;
  description: string;
  timestamp: string;
  metadata?: {
    duration?: number;
    query?: string;
    target?: string;
  };
}

export function UserActivity() {
  const [timeRange, setTimeRange] = useState('week');
  const [activityType, setActivityType] = useState('all');

  // Mock data - 在实际应用中这些数据会从API获取
  const activities: ActivityItem[] = [
    {
      id: '1',
      type: 'view',
      title: '查看了 Reitaisai 22',
      description: '浏览了事件详情页面',
      timestamp: '2024-01-15T14:30:00Z',
      metadata: { duration: 180 },
    },
    {
      id: '2',
      type: 'bookmark',
      title: '收藏了 Team Shanghai Alice',
      description: '添加到收藏列表',
      timestamp: '2024-01-15T14:25:00Z',
    },
    {
      id: '3',
      type: 'search',
      title: '搜索了 "东方Project"',
      description: '在事件列表中搜索',
      timestamp: '2024-01-15T14:20:00Z',
      metadata: { query: '东方Project' },
    },
    {
      id: '4',
      type: 'view',
      title: '查看了 Comic Market 105',
      description: '浏览了事件详情页面',
      timestamp: '2024-01-15T13:45:00Z',
      metadata: { duration: 120 },
    },
    {
      id: '5',
      type: 'share',
      title: '分享了 IOSYS',
      description: '分享了社团信息',
      timestamp: '2024-01-15T13:30:00Z',
      metadata: { target: 'twitter' },
    },
  ];

  const stats = {
    totalViews: 156,
    totalBookmarks: 12,
    totalSearches: 48,
    avgSessionTime: 8.5,
    mostActiveDay: '周三',
    favoriteCategory: '同人展',
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'view':
        return Eye;
      case 'bookmark':
        return Bookmark;
      case 'search':
        return Search;
      case 'share':
        return Share2;
      default:
        return Activity;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'view':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'bookmark':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'search':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'share':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}天前`;
    }
  };

  const filteredActivities = activities.filter(activity => {
    if (activityType !== 'all' && activity.type !== activityType) return false;
    // TODO: 根据时间范围过滤
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
              活动记录
            </h1>
            <p className="text-slate-600 dark:text-slate-400 mt-1">
              查看您的浏览历史和互动记录
            </p>
          </div>
        </div>
      </motion.div>

      {/* Stats Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            { label: '总浏览量', value: stats.totalViews, icon: Eye, color: 'text-blue-600' },
            { label: '总收藏数', value: stats.totalBookmarks, icon: Heart, color: 'text-red-600' },
            { label: '搜索次数', value: stats.totalSearches, icon: Search, color: 'text-green-600' },
            { label: '平均停留时间', value: `${stats.avgSessionTime}分钟`, icon: Clock, color: 'text-purple-600' },
          ].map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
              >
                <Card className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800 border-slate-200/50 dark:border-slate-700/50">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3">
                      <div className={`p-3 rounded-lg bg-slate-100 dark:bg-slate-800 ${stat.color}`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          {stat.label}
                        </p>
                        <p className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                          {stat.value}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </motion.div>

      {/* Activity Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800 border-slate-200/50 dark:border-slate-700/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              活动洞察
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
                  最活跃的一天
                </p>
                <p className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                  {stats.mostActiveDay}
                </p>
              </div>
              <div>
                <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
                  最喜欢的类别
                </p>
                <p className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                  {stats.favoriteCategory}
                </p>
              </div>
              <div>
                <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
                  活跃度
                </p>
                <div className="flex items-center gap-2">
                  <Progress value={75} className="flex-1" />
                  <span className="text-sm font-medium">75%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <Card className="bg-gradient-to-r from-white to-slate-50 dark:from-slate-900 dark:to-slate-800 border-slate-200/50 dark:border-slate-700/50">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-slate-500" />
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  筛选：
                </span>
              </div>
              
              <div className="flex gap-3">
                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="day">今天</SelectItem>
                    <SelectItem value="week">本周</SelectItem>
                    <SelectItem value="month">本月</SelectItem>
                    <SelectItem value="year">今年</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={activityType} onValueChange={setActivityType}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部活动</SelectItem>
                    <SelectItem value="view">浏览记录</SelectItem>
                    <SelectItem value="bookmark">收藏记录</SelectItem>
                    <SelectItem value="search">搜索记录</SelectItem>
                    <SelectItem value="share">分享记录</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Activity Timeline */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800 border-slate-200/50 dark:border-slate-700/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              活动时间线
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {filteredActivities.length > 0 ? (
              <div className="space-y-4">
                {filteredActivities.map((activity, index) => {
                  const Icon = getActivityIcon(activity.type);
                  
                  return (
                    <motion.div
                      key={activity.id}
                      className="flex items-start gap-4 p-4 rounded-lg bg-slate-50/50 dark:bg-slate-800/30 hover:bg-slate-100/50 dark:hover:bg-slate-800/50 transition-colors"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <div className={`p-2 rounded-lg ${getActivityColor(activity.type)}`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-medium text-slate-900 dark:text-slate-100">
                            {activity.title}
                          </h4>
                          <span className="text-xs text-slate-500">
                            {formatTime(activity.timestamp)}
                          </span>
                        </div>
                        
                        <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
                          {activity.description}
                        </p>
                        
                        {activity.metadata && (
                          <div className="flex gap-2">
                            {activity.metadata.duration && (
                              <Badge variant="outline" className="text-xs">
                                <Clock className="h-3 w-3 mr-1" />
                                {activity.metadata.duration}秒
                              </Badge>
                            )}
                            {activity.metadata.query && (
                              <Badge variant="outline" className="text-xs">
                                <Search className="h-3 w-3 mr-1" />
                                "{activity.metadata.query}"
                              </Badge>
                            )}
                            {activity.metadata.target && (
                              <Badge variant="outline" className="text-xs">
                                <Share2 className="h-3 w-3 mr-1" />
                                {activity.metadata.target}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-slate-300 dark:text-slate-600 mx-auto mb-3" />
                <p className="text-slate-600 dark:text-slate-400">
                  暂无活动记录
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
