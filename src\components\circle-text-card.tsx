import * as React from "react"
import Link from "next/link"
import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import { CircleAppearance } from "@/schemas"
import { TransformedCircle } from "@/app/events/[id]/utils"
import { RadixButton, RadixBadge, RadixCard, RadixCardHeader, RadixCardTitle, RadixCardDescription } from '@/components/ui/radix-components'

// 组件 Props，使用 data 包裹后端 DTO，调用方式更语义化
interface CircleTextCardProps {
  data: CircleAppearance | TransformedCircle
}

/**
 * CircleTextCard - 社团信息文字卡片
 * ------------------------------------------------------
 * 设计用途：
 *  - 用于 events/[id] 页面中的虚拟列表，展示社团简要信息
 *  - 点击卡片后弹出对话框，显示社团详细链接及『关注』按钮（占位）
 *  - 依赖 shadcn/ui 提供的 Dialog、Card、Button、Badge 组件
 *
 * Props 说明：
 *  - id           社团在数据库中的主键
 *  - name         社团名称
 *  - booth        摊位号，例如 "あ25a"
 *  - urls         后端返回的 JSON 字符串，格式示例：
 *                 {"twitter":"https://x.com/...","web":"https://..."}
 *  - created_at   创建时间（ISO 字符串）
 *  - updated_at   更新时间（ISO 字符串）
 *
 * TODO:
 *  - 接入后端关注接口，移除 Button 的 disabled 属性
 *  - 对 Dialog 添加可访问性增强（如键盘快捷键）
 *  - 若后续需要展示 logo，可在 DialogContent 中补充 <Image />
 */

export default function CircleTextCard({ data }: CircleTextCardProps) {
  const {
    circle_name,
    booth_id,
    artist_name,
    circle_urls,
  } = data



  /**
   * 解析后端返回的 JSON 字符串，失败时返回空对象
   */
  const parsedUrls = React.useMemo<Record<string, string>>(() => {
    try {
      return JSON.parse(circle_urls)
    } catch (_) {
      return {}
    }
  }, [circle_urls])



  return (
    <Dialog.Root>
      {/* 触发器：卡片本身 */}
      <Dialog.Trigger asChild>
        <RadixCard className="w-full cursor-pointer transition-shadow hover:shadow-md" data-testid="circle-card">
          <RadixCardHeader className="flex-row items-center gap-3 px-4 py-3">
            <RadixCardTitle className="text-base font-medium flex-1 truncate">
              {circle_name}
            </RadixCardTitle>
            {booth_id && (
              <RadixCardDescription>
                <RadixBadge variant="outline">{booth_id}</RadixBadge>
              </RadixCardDescription>
            )}
            {artist_name && (
              <RadixCardDescription>
                <RadixBadge variant="outline">{artist_name}</RadixBadge>
              </RadixCardDescription>
            )}
          </RadixCardHeader>
        </RadixCard>
      </Dialog.Trigger>

      {/* 弹出层内容 */}
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-md translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg">
          {/* Dialog Header */}
          <div className="flex flex-col space-y-1.5 text-center sm:text-left">
            <Dialog.Title className="text-lg font-semibold leading-none tracking-tight">
              {circle_name}
            </Dialog.Title>
            <Dialog.Description className="text-sm text-muted-foreground">
              社团相关链接
            </Dialog.Description>
          </div>

          {/* 关闭按钮 */}
          <Dialog.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Dialog.Close>

          {/* 链接列表 */}
          <div className="flex flex-col gap-2 py-2">
            {Object.entries(parsedUrls).length === 0 && (
              <p className="text-sm text-muted-foreground">暂无链接信息</p>
            )}
            {Object.entries(parsedUrls).map(([key, value]) => (
              <a
                key={key}
                href={value}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary underline underline-offset-2 hover:opacity-80"
              >
                {key}
              </a>
            ))}
          </div>

          {/* Dialog Footer */}
          <div className="flex flex-wrap gap-2 sm:flex-nowrap">
            {/* 查看详情入口 */}
            {/**
             * 说明：
             * - 直接在弹窗中提供跳转按钮，最小化改动即可完成入口需求。
             * - id 字段后端可能返回 id / circle_id，这里做兼容处理。
             */}
            <RadixButton asChild variant="link">
              <Link href={`/circles/${("id" in data ? (data as any).id : (data as any).circle_id) ?? ""}`}>
                查看详情
              </Link>
            </RadixButton>

            {/* 占位按钮，后续接入后端接口 */}
            <RadixButton variant="secondary" disabled>
              关注（待实现）
            </RadixButton>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}
