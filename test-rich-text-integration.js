// 测试富文本编辑器集成
const API_BASE = 'http://127.0.0.1:9830';
const EVENT_ID = '785e0d4c-9dae-4fe7-8c4b-a503ffc14248';

async function testRichTextAPI() {
  console.log('🧪 测试富文本编辑器API集成...\n');

  try {
    // 1. 测试获取内容
    console.log('1️⃣ 测试获取事件内容...');
    const getResponse = await fetch(`${API_BASE}/content/event/${EVENT_ID}`);
    console.log(`状态码: ${getResponse.status}`);
    
    if (getResponse.ok) {
      const content = await getResponse.json();
      console.log('✅ 获取内容成功:', content);
    } else {
      console.log('⚠️ 内容不存在，这是正常的（首次访问）');
    }

    // 2. 测试创建/更新内容
    console.log('\n2️⃣ 测试创建/更新内容...');
    const testContent = {
      introduction: '<h2>测试介绍</h2><p>这是一个<strong>测试事件</strong>的介绍内容。</p>',
      highlights: '<h3>活动亮点</h3><ul><li>精彩展示</li><li>互动体验</li><li>专业交流</li></ul>',
      guide: '<h3>参与指南</h3><p>请按照以下步骤参与活动：</p><ol><li>提前注册</li><li>准时到达</li><li>遵守规则</li></ol>',
      notices: '<h3>重要通知</h3><p class="text-red-500">请注意活动时间变更！</p>'
    };

    const updateResponse = await fetch(`${API_BASE}/content/event/${EVENT_ID}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testContent)
    });

    console.log(`状态码: ${updateResponse.status}`);
    
    if (updateResponse.ok) {
      const result = await updateResponse.json();
      console.log('✅ 更新内容成功:', result);
    } else {
      const error = await updateResponse.text();
      console.log('❌ 更新失败:', error);
    }

    // 3. 测试单个内容类型更新
    console.log('\n3️⃣ 测试单个内容类型更新...');
    const singleUpdateResponse = await fetch(`${API_BASE}/content/event/${EVENT_ID}/introduction`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content: '<h2>更新后的介绍</h2><p>这是通过单个API更新的内容。</p><p>包含<em>斜体</em>和<strong>粗体</strong>文本。</p>'
      })
    });

    console.log(`状态码: ${singleUpdateResponse.status}`);
    
    if (singleUpdateResponse.ok) {
      const result = await singleUpdateResponse.json();
      console.log('✅ 单个更新成功:', result);
    } else {
      const error = await singleUpdateResponse.text();
      console.log('❌ 单个更新失败:', error);
    }

    // 4. 验证更新结果
    console.log('\n4️⃣ 验证更新结果...');
    const verifyResponse = await fetch(`${API_BASE}/content/event/${EVENT_ID}`);
    
    if (verifyResponse.ok) {
      const finalContent = await verifyResponse.json();
      console.log('✅ 最终内容:', finalContent);
    } else {
      console.log('❌ 验证失败');
    }

    console.log('\n🎉 API测试完成！');
    console.log('\n📝 下一步：');
    console.log('1. 打开浏览器访问: http://localhost:3003/events/' + EVENT_ID);
    console.log('2. 点击"详细信息"标签页查看富文本内容');
    console.log('3. 访问管理页面: http://localhost:3003/admin/events/' + EVENT_ID + '/edit');
    console.log('4. 在"内容管理"部分编辑富文本内容');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 运行测试
testRichTextAPI();
