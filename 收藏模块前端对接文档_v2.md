# 收藏模块前端对接文档

> 📝 **版本**: v2.0.0  
> 🕒 **更新时间**: 2025-08-02  
> 👥 **目标读者**: 前端开发者

## 📋 快速导航

- [🚀 快速开始](#-快速开始)
- [🔌 API 接口](#-api-接口)
- [💻 前端集成](#-前端集成)
- [🎨 UI 组件示例](#-ui-组件示例)
- [🛡️ 安全注意事项](#️-安全注意事项)
- [❓ 常见问题](#-常见问题)

## 🚀 快速开始

### 基础信息

**模块功能**: 为用户提供完整的社团收藏功能  
**支持操作**: 收藏切换、列表查询、状态检查、统计信息、批量操作  
**认证要求**: 所有操作都需要用户登录  
**分页支持**: 传统分页和游标分页双重支持

### 5分钟上手

```typescript
// 1. 切换收藏状态
const toggleMutation = useToggleBookmark();
toggleMutation.mutate('circle-123');

// 2. 获取收藏列表
const { data: bookmarks } = useBookmarks({ page: 1, pageSize: 20 });

// 3. 检查收藏状态
const { data: status } = useBookmarkStatus('circle-123');

// 4. 在组件中使用
function BookmarkButton({ circleId }: { circleId: string }) {
  const { data: status, isLoading } = useBookmarkStatus(circleId);
  const toggleMutation = useToggleBookmark();

  const handleToggle = () => {
    toggleMutation.mutate(circleId);
  };

  return (
    <button
      onClick={handleToggle}
      disabled={isLoading || toggleMutation.isPending}
    >
      {status?.isBookmarked ? '已收藏' : '收藏'}
    </button>
  );
}
```

## 🔌 API 接口

### 接口概览

| 方法   | 路径                                  | 功能         | 权限     |
| ------ | ------------------------------------- | ------------ | -------- |
| `POST` | `/circles/{circleId}/bookmark`        | 切换收藏状态 | 登录用户 |
| `GET`  | `/circles/{circleId}/bookmark/status` | 检查收藏状态 | 登录用户 |
| `GET`  | `/user/bookmarks`                     | 获取收藏列表 | 登录用户 |
| `GET`  | `/user/bookmarks/stats`               | 获取收藏统计 | 登录用户 |
| `POST` | `/user/bookmarks/batch`               | 批量收藏操作 | 登录用户 |

### 数据类型定义

```typescript
// 基础收藏对象
interface Bookmark {
  id: string;
  user_id: string;
  circle_id: string;
  created_at: string;
  updated_at: string;
}

// API 响应格式（项目标准）
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 切换收藏响应
interface ToggleBookmarkResponse {
  isBookmarked: boolean;
}

// 收藏状态响应
interface BookmarkStatusResponse {
  isBookmarked: boolean;
  bookmarkId: string | null;
  createdAt: string | null;
}

// 收藏列表查询参数
interface BookmarkListQuery {
  // 传统分页参数
  page?: number;
  pageSize?: number;

  // 游标分页参数（性能优化）
  cursor?: string;

  // 查询参数
  search?: string;
  sortBy?: 'created_at' | 'circle_name';
  sortOrder?: 'asc' | 'desc';
}

// 收藏列表项
interface BookmarkListItem {
  id: string;
  created_at: string;
  circle: {
    id: string;
    name: string;
    category: string;
    urls: string | null;
    created_at: string;
    updated_at: string;
  };
}

// 收藏列表响应
interface BookmarkListResponse {
  items: BookmarkListItem[];
  // 传统分页信息
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  // 游标分页信息
  nextCursor: string | null;
  hasMore: boolean;
}

// 收藏统计响应
interface BookmarkStatsResponse {
  totalBookmarks: number;
  recentBookmarks: number; // 最近7天
  categoryCounts: Record<string, number>;
}

// 批量操作请求
interface BookmarkBatchRequest {
  action: 'add' | 'remove';
  circleIds: string[]; // 最多50个
}

// 批量操作响应
interface BookmarkBatchResponse {
  success: string[];
  failed: Array<{
    circleId: string;
    reason: string;
  }>;
  total: number;
  successCount: number;
  failedCount: number;
}
```

### 详细接口说明

#### 1. 切换收藏状态

```typescript
POST /circles/{circleId}/bookmark

// 路径参数
{
  circleId: string; // 社团ID，如 'circle-123'
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例
{
  "code": 0,
  "message": "已加入收藏", // 或 "已取消收藏"
  "data": {
    "isBookmarked": true // 当前收藏状态
  }
}
```

**功能说明**:

- 如果用户未收藏该社团，则添加收藏，返回 `isBookmarked: true`
- 如果用户已收藏该社团，则取消收藏，返回 `isBookmarked: false`
- 操作是原子性的，不会出现重复收藏的情况

#### 2. 检查收藏状态

```typescript
GET /circles/{circleId}/bookmark/status

// 路径参数
{
  circleId: string; // 社团ID
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例
{
  "code": 0,
  "message": "获取收藏状态成功",
  "data": {
    "isBookmarked": true,
    "bookmarkId": "bookmark-uuid",
    "createdAt": "2025-01-01T00:00:00Z"
  }
}
```

#### 3. 获取收藏列表

```typescript
GET /user/bookmarks

// 查询参数
{
  page?: number;        // 页码，默认 1
  pageSize?: number;    // 每页数量，默认 20，最大 100
  cursor?: string;      // 游标分页（推荐）
  search?: string;      // 搜索关键词
  sortBy?: 'created_at' | 'circle_name';  // 排序字段
  sortOrder?: 'asc' | 'desc';             // 排序方向
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例
{
  "code": 0,
  "message": "获取收藏列表成功",
  "data": {
    "items": [
      {
        "id": "bookmark-uuid",
        "created_at": "2025-01-01T00:00:00Z",
        "circle": {
          "id": "circle-uuid",
          "name": "某某工作室",
          "category": "original",
          "urls": "{\"twitter\":\"@example\"}",
          "created_at": "2025-01-01T00:00:00Z",
          "updated_at": "2025-01-01T00:00:00Z"
        }
      }
    ],
    "total": 15,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1,
    "nextCursor": null,
    "hasMore": false
  }
}
```

#### 4. 获取收藏统计

```typescript
GET /user/bookmarks/stats

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例
{
  "code": 0,
  "message": "获取收藏统计成功",
  "data": {
    "totalBookmarks": 25,
    "recentBookmarks": 3,
    "categoryCounts": {
      "original": 15,
      "derivative": 10
    }
  }
}
```

## 💻 前端集成

### React Query Hooks

```typescript
// hooks/useBookmark.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { request } from '@/lib/http';

// 切换收藏状态
export function useToggleBookmark() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (circleId: string) => {
      return request<ToggleBookmarkResponse>(`/circles/${circleId}/bookmark`, {
        method: 'POST',
      });
    },
    onSuccess: (data, circleId) => {
      // 更新收藏状态缓存
      queryClient.setQueryData(['bookmark-status', circleId], {
        isBookmarked: data.isBookmarked,
        bookmarkId: data.isBookmarked ? 'temp-id' : null,
        createdAt: data.isBookmarked ? new Date().toISOString() : null,
      });

      // 刷新收藏列表和统计
      queryClient.invalidateQueries({ queryKey: ['bookmarks'] });
      queryClient.invalidateQueries({ queryKey: ['bookmark-stats'] });
    },
  });
}

// 检查收藏状态
export function useBookmarkStatus(circleId: string) {
  return useQuery({
    queryKey: ['bookmark-status', circleId],
    queryFn: () =>
      request<BookmarkStatusResponse>(`/circles/${circleId}/bookmark/status`),
    staleTime: 10 * 60 * 1000, // 10分钟缓存
    enabled: !!circleId,
  });
}

// 获取收藏列表
export function useBookmarks(query: BookmarkListQuery) {
  return useQuery({
    queryKey: ['bookmarks', query],
    queryFn: () => {
      const params = new URLSearchParams();
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
      return request<BookmarkListResponse>(`/user/bookmarks?${params}`);
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
}

// 获取收藏统计
export function useBookmarkStats() {
  return useQuery({
    queryKey: ['bookmark-stats'],
    queryFn: () => request<BookmarkStatsResponse>('/user/bookmarks/stats'),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
}

// 批量操作收藏
export function useBatchBookmarks() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: BookmarkBatchRequest) =>
      request<BookmarkBatchResponse>('/user/bookmarks/batch', {
        method: 'POST',
        body: JSON.stringify(request),
      }),
    onSuccess: () => {
      // 刷新所有相关缓存
      queryClient.invalidateQueries({ queryKey: ['bookmarks'] });
      queryClient.invalidateQueries({ queryKey: ['bookmark-status'] });
      queryClient.invalidateQueries({ queryKey: ['bookmark-stats'] });
    },
  });
}
```

### 错误处理

```typescript
// utils/bookmarkErrorHandler.ts
export function handleBookmarkError(error: any) {
  if (error?.code === 20001) {
    return '请先登录后再进行收藏操作';
  }
  if (error?.code === 40001) {
    return '社团ID无效';
  }
  if (error?.code === 10002) {
    return '社团不存在';
  }
  if (error?.code === 50001) {
    return '服务器错误，请重试';
  }
  return '操作失败，请重试';
}

// 在组件中使用
const toggleMutation = useToggleBookmark();

const handleToggle = async (circleId: string) => {
  try {
    await toggleMutation.mutateAsync(circleId);
    toast.success('操作成功');
  } catch (error) {
    const message = handleBookmarkError(error);
    toast.error(message);
  }
};
```

### 游标分页支持

```typescript
// 无限滚动收藏列表
export function useInfiniteBookmarks(
  baseQuery: Omit<BookmarkListQuery, 'cursor'>
) {
  return useInfiniteQuery({
    queryKey: ['bookmarks-infinite', baseQuery],
    queryFn: ({ pageParam }) =>
      request<BookmarkListResponse>('/user/bookmarks', {
        params: {
          ...baseQuery,
          cursor: pageParam,
          pageSize: 20,
        },
      }),
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    initialPageParam: undefined as string | undefined,
  });
}
```

## 🎨 UI 组件示例

### 基础收藏按钮

```tsx
// components/BookmarkButton.tsx
import { useState } from 'react';
import { Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToggleBookmark, useBookmarkStatus } from '@/hooks/useBookmark';
import { toast } from 'sonner';

interface BookmarkButtonProps {
  circleId: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
  showText?: boolean;
  className?: string;
}

export function BookmarkButton({
  circleId,
  size = 'md',
  variant = 'outline',
  showText = true,
  className = '',
}: BookmarkButtonProps) {
  const { data: status, isLoading } = useBookmarkStatus(circleId);
  const toggleMutation = useToggleBookmark();

  const handleToggle = async () => {
    try {
      const result = await toggleMutation.mutateAsync(circleId);
      const message = result.isBookmarked ? '已加入收藏' : '已取消收藏';
      toast.success(message);
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const sizeClasses = {
    sm: 'h-8 px-2 text-sm',
    md: 'h-9 px-3',
    lg: 'h-10 px-4',
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleToggle}
      disabled={isLoading || toggleMutation.isPending}
      className={`${sizeClasses[size]} ${className}`}
    >
      <Heart
        className={`w-4 h-4 ${status?.isBookmarked ? 'fill-red-500 text-red-500' : ''} ${
          showText ? 'mr-2' : ''
        }`}
      />
      {showText && (
        <span>
          {toggleMutation.isPending
            ? '处理中...'
            : status?.isBookmarked
              ? '已收藏'
              : '收藏'}
        </span>
      )}
    </Button>
  );
}
```

### 收藏列表组件

```tsx
// components/BookmarkList.tsx
import { useState } from 'react';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { useBookmarks } from '@/hooks/useBookmark';

export function BookmarkList() {
  const [query, setQuery] = useState<BookmarkListQuery>({
    page: 1,
    pageSize: 20,
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  const { data, isLoading, error } = useBookmarks(query);

  const handleSearch = (search: string) => {
    setQuery((prev) => ({ ...prev, search, page: 1 }));
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading bookmarks</div>;

  return (
    <div className="space-y-4">
      {/* 搜索和排序 */}
      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search circles..."
            className="pl-10"
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        <Select
          value={query.sortBy}
          onValueChange={(sortBy) => setQuery((prev) => ({ ...prev, sortBy }))}
        >
          <option value="created_at">Date Added</option>
          <option value="circle_name">Circle Name</option>
        </Select>
      </div>

      {/* 收藏列表 */}
      <div className="grid gap-4">
        {data?.items.map((bookmark) => (
          <div key={bookmark.id} className="border rounded-lg p-4">
            <h3 className="font-semibold">{bookmark.circle.name}</h3>
            <p className="text-sm text-muted-foreground">
              {bookmark.circle.category}
            </p>
            <p className="text-xs text-muted-foreground mt-2">
              Bookmarked on {new Date(bookmark.created_at).toLocaleDateString()}
            </p>
          </div>
        ))}
      </div>

      {/* 分页 */}
      {data && data.totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            disabled={query.page === 1}
            onClick={() =>
              setQuery((prev) => ({ ...prev, page: prev.page! - 1 }))
            }
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {query.page} of {data.totalPages}
          </span>
          <Button
            variant="outline"
            disabled={query.page === data.totalPages}
            onClick={() =>
              setQuery((prev) => ({ ...prev, page: prev.page! + 1 }))
            }
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
```

### 收藏统计组件

```tsx
// components/BookmarkStats.tsx
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useBookmarkStats } from '@/hooks/useBookmark';

export function BookmarkStats() {
  const { data: stats, isLoading } = useBookmarkStats();

  if (isLoading) return <div>Loading stats...</div>;
  if (!stats) return null;

  return (
    <div className="grid gap-4 md:grid-cols-3">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Bookmarks</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalBookmarks}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Recent (7 days)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.recentBookmarks}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-1">
            {Object.entries(stats.categoryCounts).map(([category, count]) => (
              <div key={category} className="flex justify-between text-sm">
                <span className="capitalize">{category}</span>
                <span className="font-medium">{count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

## 🛡️ 安全注意事项

### 认证要求

```typescript
// 确保用户已登录
import { useAuth } from '@/hooks/useAuth';

function BookmarkFeature({ circleId }: { circleId: string }) {
  const { isAuthenticated, user } = useAuth();

  if (!isAuthenticated) {
    return (
      <Button variant="outline" onClick={() => router.push('/login')}>
        登录后收藏
      </Button>
    );
  }

  return <BookmarkButton circleId={circleId} />;
}
```

### 数据验证

```typescript
// 验证 circleId 格式
function validateCircleId(circleId: string): boolean {
  return /^[a-zA-Z0-9-_]+$/.test(circleId) && circleId.length > 0;
}

// 在使用前验证
const handleBookmark = (circleId: string) => {
  if (!validateCircleId(circleId)) {
    toast.error('无效的社团ID');
    return;
  }

  toggleMutation.mutate(circleId);
};
```

### 防抖处理

```typescript
// 防止重复点击
import { useDebouncedCallback } from 'use-debounce';

export function BookmarkButtonWithDebounce({ circleId }: { circleId: string }) {
  const toggleMutation = useToggleBookmark();

  const debouncedToggle = useDebouncedCallback(
    (id: string) => {
      toggleMutation.mutate(id);
    },
    300 // 300ms 防抖
  );

  return (
    <Button
      onClick={() => debouncedToggle(circleId)}
      disabled={toggleMutation.isPending}
    >
      收藏
    </Button>
  );
}
```

## ❓ 常见问题

### Q: 如何处理用户登录状态变化？

**A**: 监听认证状态变化，清理或同步收藏数据：

```typescript
import { useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useQueryClient } from '@tanstack/react-query';

export function BookmarkSyncProvider({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, user } = useAuth();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!isAuthenticated) {
      // 用户登出时清理收藏相关缓存
      queryClient.removeQueries({ queryKey: ['bookmarks'] });
      queryClient.removeQueries({ queryKey: ['bookmark-status'] });
      queryClient.removeQueries({ queryKey: ['bookmark-stats'] });
    }
  }, [isAuthenticated, user?.id, queryClient]);

  return <>{children}</>;
}
```

### Q: 如何处理网络错误和重试？

**A**: 使用 React Query 的内置重试机制：

```typescript
export function useToggleBookmarkWithRetry() {
  return useMutation({
    mutationFn: async (circleId: string) => {
      return request<ToggleBookmarkResponse>(`/circles/${circleId}/bookmark`, {
        method: 'POST',
      });
    },
    retry: (failureCount, error) => {
      // 网络错误重试，认证错误不重试
      if (error?.code === 20001) return false;
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}
```

### Q: 如何实现批量选择和操作？

**A**: 使用状态管理批量选择：

```typescript
function BatchBookmarkManager({ circles }: { circles: Circle[] }) {
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const batchMutation = useBatchBookmarks();

  const handleSelectAll = () => {
    setSelectedIds(circles.map(c => c.id));
  };

  const handleBatchAdd = async () => {
    if (selectedIds.length === 0) return;

    try {
      await batchMutation.mutateAsync({
        action: 'add',
        circleIds: selectedIds,
      });
      setSelectedIds([]);
      toast.success('批量收藏完成');
    } catch (error) {
      toast.error('批量操作失败');
    }
  };

  return (
    <div>
      <div className="flex gap-2 mb-4">
        <Button onClick={handleSelectAll}>全选</Button>
        <Button
          onClick={handleBatchAdd}
          disabled={selectedIds.length === 0 || batchMutation.isPending}
        >
          批量收藏 ({selectedIds.length})
        </Button>
      </div>

      {circles.map(circle => (
        <div key={circle.id} className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={selectedIds.includes(circle.id)}
            onChange={(e) => {
              if (e.target.checked) {
                setSelectedIds(prev => [...prev, circle.id]);
              } else {
                setSelectedIds(prev => prev.filter(id => id !== circle.id));
              }
            }}
          />
          <span>{circle.name}</span>
        </div>
      ))}
    </div>
  );
}
```

---

## 📚 相关文档

- [API 规范文档](./api-specification.md)
- [社团模块文档](./circle-module.md)
- [用户认证文档](./auth-module.md)
- [错误处理指南](./error-handling.md)

---

**📞 技术支持**: 如有问题请联系后端团队或查看项目 Issues
{
"Authorization": "Bearer {token}", // 必需
"Content-Type": "application/json"
}

// 请求体
{
"action": "add", // 或 "remove"
"circleIds": ["circle-1", "circle-2", "circle-3"]
}

// 响应示例
{
"code": 0,
"message": "批量收藏操作完成，成功 2 个，失败 1 个",
"data": {
"success": ["circle-1", "circle-2"],
"failed": [
{
"circleId": "circle-3",
"reason": "社团不存在"
}
],
"total": 3,
"successCount": 2,
"failedCount": 1
}
}

```

```
